import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject } from '@angular/core';
import { ClientService } from '../../services/client.service';
import { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Client, ClientModel, CreateClientSimpleDto, UpdateClientDto } from '../../Model/Client';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';

export interface DialogData {
  id: string;
  action: string;
  client: Client;
}

@Component({
  selector: 'app-form-client',
  templateUrl: './form-client.component.html',
  styleUrls: ['./form-client.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    CommonModule,
  ],
})
export class FormClientComponent {
  action: string;
  dialogTitle?: string;
  isDetails = false;
  clientForm?: UntypedFormGroup;
  client: ClientModel;

  constructor(
    public dialogRef: MatDialogRef<FormClientComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    public clientService: ClientService,
    private fb: UntypedFormBuilder
  ) {
    // Set the defaults
    this.action = data.action;
    if (this.action === 'edit') {
      this.isDetails = false;
      this.dialogTitle = `Modifier: ${data.client.code}`;
      this.client = new ClientModel(data.client);
      this.clientForm = this.createClientForm();
    } else if (this.action === 'details') {
      this.client = new ClientModel(data.client);
      this.isDetails = true;
    } else {
      this.isDetails = false;
      this.dialogTitle = 'Nouveau Client';
      const blankObject = {} as Client;
      this.client = new ClientModel(blankObject);
      this.clientForm = this.createClientForm();
    }
  }

  formControl = new UntypedFormControl('', [
    Validators.required,
  ]);

  getErrorMessage() {
    return this.formControl.hasError('required')
      ? 'Champ requis'
      : this.formControl.hasError('email')
      ? 'Format d\'email invalide'
      : '';
  }

  createClientForm(): UntypedFormGroup {
    return this.fb.group({
      id: [this.client.id],
      code: [this.client.code, [Validators.required, Validators.minLength(2)]],
      syntax: [this.client.syntax],
      matFiscal: [this.client.matFiscal, [this.validateMatFiscal]],
      email: [this.client.email, [Validators.email]],
      telephone: [this.client.telephone, [this.validateTelephone]],
    });
  }

  submit() {
    if (this.clientForm?.valid) {
      if (this.action === 'add') {
        this.confirmAdd();
      } else if (this.action === 'edit') {
        this.confirmEdit();
      }
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  public confirmAdd(): void {
    if (this.clientForm?.valid) {
      const formValue = this.clientForm.getRawValue();
      
      // Validation supplémentaire côté client
      if (!formValue.code || formValue.code.trim() === '') {
        console.error('Code client requis');
        return;
      }

      const createDto: CreateClientSimpleDto = {
        code: formValue.code.trim(),
        syntax: formValue.syntax?.trim(),
        matFiscal: formValue.matFiscal?.trim(),
        email: formValue.email?.trim(),
        telephone: formValue.telephone?.trim()
      };

      console.log('Tentative de création avec les données:', createDto);

      this.clientService.createClient(createDto).subscribe({
        next: (result) => {
          console.log('Client créé avec succès dans le composant:', result);
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('Erreur lors de la création du client dans le composant:', error);
          alert(`Erreur lors de la création: ${error.message}`);
        }
      });
    } else {
      console.error('Formulaire invalide:', this.clientForm?.errors);
    }
  }

  public confirmEdit(): void {
    if (this.clientForm?.valid) {
      const formValue = this.clientForm.getRawValue();
      const updateDto: UpdateClientDto = {
        code: formValue.code?.trim(),
        syntax: formValue.syntax?.trim(),
        matFiscal: formValue.matFiscal?.trim(),
        email: formValue.email?.trim(),
        telephone: formValue.telephone?.trim()
      };

      this.clientService.updateClient(this.client.id, updateDto).subscribe({
        next: () => {
          this.dialogRef.close(true);
        },
        error: (error) => {
          console.error('Erreur lors de la modification du client:', error);
          alert(`Erreur lors de la modification: ${error.message}`);
        }
      });
    }
  }

  // Validation personnalisée pour le matricule fiscal tunisien
  validateMatFiscal(control: UntypedFormControl) {
    const value = control.value;
    if (!value) return null;
    
    // Format: 7 chiffres + 3 lettres + 3 chiffres (ex: 1234567ABC123)
    const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$/;
    return matFiscalPattern.test(value) ? null : { invalidMatFiscal: true };
  }

  // Validation personnalisée pour le téléphone
  validateTelephone(control: UntypedFormControl) {
    const value = control.value;
    if (!value) return null;
    
    // Accepte différents formats de téléphone
    const phonePattern = /^[+]?[0-9\s\-\(\)]{8,15}$/;
    return phonePattern.test(value) ? null : { invalidPhone: true };
  }
}
