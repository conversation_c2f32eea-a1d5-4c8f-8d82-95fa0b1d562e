import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ClientService } from '../../services/client.service';
import { HttpClient } from '@angular/common/http';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatSort, MatSortModule } from '@angular/material/sort';
import { Client } from '../../Model/Client';
import { DataSource } from '@angular/cdk/collections';
import { FormClientComponent } from '../form-client/form-client.component';
import { ClientDeleteComponent } from '../client-delete/client-delete.component';
import { BulkDeleteClientConfirmationComponent } from '../bulk-delete-client-confirmation/bulk-delete-client-confirmation.component';
import { BehaviorSubject, fromEvent, merge, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SelectionModel } from '@angular/cdk/collections';
import { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';
import { Direction } from '@angular/cdk/bidi';
import { TableExportUtil, TableElement } from '@shared';
import { formatDate, NgClass, DatePipe, CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRippleModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-client',
  templateUrl: './client.component.html',
  styleUrls: ['./client.component.scss'],
  standalone: true,
  imports: [
    BreadcrumbComponent,
    MatTooltipModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSortModule,
    NgClass,
    MatCheckboxModule,
    MatRippleModule,
    MatProgressSpinnerModule,
    MatPaginatorModule,
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
  ],
})
export class ClientComponent extends UnsubscribeOnDestroyAdapter implements OnInit {
  displayedColumns = [
    'select',
    'code',
    'syntax',
    'matFiscal',
    'email',
    'telephone',
    'actions',
  ];
  exampleDatabase?: ClientService;
  dataSource!: ExampleDataSource;
  selection = new SelectionModel<Client>(true, []);
  id?: string;
  client?: Client;

  breadscrums = [
    {
      title: 'Gestion des Clients',
      items: ['Admin', 'Gestion'],
      active: 'Clients',
    },
  ];

  constructor(
    public httpClient: HttpClient,
    public dialog: MatDialog,
    public clientService: ClientService,
    private snackBar: MatSnackBar
  ) {
    super();
  }

  @ViewChild(MatPaginator, { static: true })
  paginator!: MatPaginator;
  @ViewChild(MatSort, { static: true })
  sort!: MatSort;
  @ViewChild('filter', { static: true }) filter?: ElementRef;

  ngOnInit() {
    this.loadData();
  }

  refresh() {
    this.loadData();
  }

  addNew() {
    let tempDirection: Direction;
    if (localStorage.getItem('isRtl') === 'true') {
      tempDirection = 'rtl';
    } else {
      tempDirection = 'ltr';
    }
    const dialogRef = this.dialog.open(FormClientComponent, {
      data: {
        client: this.client,
        action: 'add',
      },
      direction: tempDirection,
    });
    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
      if (result === 1) {
        this.exampleDatabase?.dataChange.value.unshift(
          this.clientService.getDialogData()
        );
        this.refreshTable();
        this.showNotification(
          'snackbar-success',
          'Client ajouté avec succès...!!!',
          'bottom',
          'center'
        );
      }
    });
  }

  editCall(row: Client) {
    this.id = row.id;
    let tempDirection: Direction;
    if (localStorage.getItem('isRtl') === 'true') {
      tempDirection = 'rtl';
    } else {
      tempDirection = 'ltr';
    }
    const dialogRef = this.dialog.open(FormClientComponent, {
      data: {
        client: row,
        action: 'edit',
      },
      direction: tempDirection,
    });
    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
      if (result === 1) {
        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(
          (x) => x.id === this.id
        );
        if (foundIndex != null && this.exampleDatabase) {
          this.exampleDatabase.dataChange.value[foundIndex] =
            this.clientService.getDialogData();
          this.refreshTable();
          this.showNotification(
            'black',
            'Client modifié avec succès...!!!',
            'bottom',
            'center'
          );
        }
      }
    });
  }

  deleteItem(row: Client) {
    this.id = row.id;
    let tempDirection: Direction;
    if (localStorage.getItem('isRtl') === 'true') {
      tempDirection = 'rtl';
    } else {
      tempDirection = 'ltr';
    }
    const dialogRef = this.dialog.open(ClientDeleteComponent, {
      width: '500px',
      maxWidth: '90vw',
      data: row,
      direction: tempDirection,
    });
    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {
      if (result === 1) {
        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(
          (x) => x.id === this.id
        );
        if (foundIndex != null && this.exampleDatabase) {
          this.exampleDatabase.dataChange.value.splice(foundIndex, 1);
          this.refreshTable();
          this.showNotification(
            'snackbar-danger',
            'Client supprimé avec succès...!!!',
            'bottom',
            'center'
          );
        }
      }
    });
  }

  private refreshTable() {
    this.paginator._changePageSize(this.paginator.pageSize);
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.renderedData.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.renderedData.forEach((row) =>
          this.selection.select(row)
        );
  }

  removeSelectedRows() {
    const totalSelect = this.selection.selected.length;
    
    if (totalSelect === 0) {
      this.showNotification(
        'snackbar-warning',
        'Aucun client sélectionné pour la suppression',
        'bottom',
        'center'
      );
      return;
    }

    let tempDirection: Direction;
    if (localStorage.getItem('isRtl') === 'true') {
      tempDirection = 'rtl';
    } else {
      tempDirection = 'ltr';
    }

    const dialogRef = this.dialog.open(BulkDeleteClientConfirmationComponent, {
      width: '600px',
      maxWidth: '90vw',
      data: {
        selectedClients: this.selection.selected,
        totalCount: totalSelect
      },
      direction: tempDirection,
    });

    this.subs.sink = dialogRef.afterClosed().subscribe((confirmed) => {
      if (confirmed) {
        const selectedIds = this.selection.selected.map(item => item.id);
        
        this.clientService.deleteSelectedClients(selectedIds).subscribe({
          next: () => {
            this.selection.selected.forEach((item) => {
              const index: number = this.dataSource.renderedData.findIndex(
                (d) => d === item
              );
              if (index !== -1) {
                this.exampleDatabase?.dataChange.value.splice(index, 1);
              }
            });
            
            this.selection = new SelectionModel<Client>(true, []);
            this.refreshTable();
            this.clientService.getAllClients().subscribe();
            
            this.showNotification(
              'snackbar-danger',
              `${totalSelect} client(s) supprimé(s) avec succès !`,
              'bottom',
              'center'
            );
          },
          error: (error) => {
            console.error('Erreur lors de la suppression des clients:', error);
            this.showNotification(
              'snackbar-danger',
              'Erreur lors de la suppression des clients. Veuillez réessayer.',
              'bottom',
              'center'
            );
          }
        });
      }
    });
  }

  public loadData() {
    this.exampleDatabase = this.clientService;
    this.dataSource = new ExampleDataSource(
      this.exampleDatabase,
      this.paginator,
      this.sort
    );
    this.subs.sink = fromEvent(this.filter?.nativeElement, 'keyup').subscribe(
      () => {
        if (!this.dataSource) {
          return;
        }
        this.dataSource.filter = this.filter?.nativeElement.value;
      }
    );
  }

  exportExcel() {
    const exportData: Partial<TableElement>[] =
      this.dataSource.filteredData.map((x) => ({
        'Code Client': x.code,
        'Raison Sociale': x.syntax,
        'Matricule Fiscal': x.matFiscal,
        'Email': x.email,
        'Téléphone': x.telephone,
      }));

    TableExportUtil.exportToExcel(exportData, 'clients');
  }

  showNotification(
    colorName: string,
    text: string,
    placementFrom: MatSnackBarVerticalPosition,
    placementAlign: MatSnackBarHorizontalPosition
  ) {
    this.snackBar.open(text, '', {
      duration: 2000,
      verticalPosition: placementFrom,
      horizontalPosition: placementAlign,
      panelClass: colorName,
    });
  }
}

export class ExampleDataSource extends DataSource<Client> {
  filterChange = new BehaviorSubject('');
  get filter(): string {
    return this.filterChange.value;
  }
  set filter(filter: string) {
    this.filterChange.next(filter);
  }
  filteredData: Client[] = [];
  renderedData: Client[] = [];
  constructor(
    public exampleDatabase: ClientService,
    public paginator: MatPaginator,
    public _sort: MatSort
  ) {
    super();
  }

  connect(): Observable<Client[]> {
    const displayDataChanges = [
      this.exampleDatabase.dataChange,
      this._sort.sortChange,
      this.filterChange,
      this.paginator.page,
    ];
    this.exampleDatabase.getAllClients();
    return merge(...displayDataChanges).pipe(
      map(() => {
        this.filteredData = this.exampleDatabase.data
          .slice()
          .filter((client: Client) => {
            const searchStr = (
              client.code +
              client.syntax +
              client.matFiscal +
              client.email +
              client.telephone
            ).toLowerCase();
            return searchStr.indexOf(this.filter.toLowerCase()) !== -1;
          });
        const sortedData = this.sortData(this.filteredData.slice());
        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;
        this.renderedData = sortedData.splice(
          startIndex,
          this.paginator.pageSize
        );
        return this.renderedData;
      })
    );
  }
  disconnect() {
    // disconnect
  }
  sortData(data: Client[]): Client[] {
    if (!this._sort.active || this._sort.direction === '') {
      return data;
    }
    return data.sort((a, b) => {
      let propertyA: number | string = '';
      let propertyB: number | string = '';
      switch (this._sort.active) {
        case 'id':
          [propertyA, propertyB] = [a.id, b.id];
          break;
        case 'code':
          [propertyA, propertyB] = [a.code, b.code];
          break;
        case 'syntax':
          [propertyA, propertyB] = [a.syntax || '', b.syntax || ''];
          break;
        case 'matFiscal':
          [propertyA, propertyB] = [a.matFiscal || '', b.matFiscal || ''];
          break;
        case 'email':
          [propertyA, propertyB] = [a.email || '', b.email || ''];
          break;
        case 'telephone':
          [propertyA, propertyB] = [a.telephone || '', b.telephone || ''];
          break;
      }
      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;
      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;
      return (
        (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1)
      );
    });
  }
}
