import { Routes } from "@angular/router";
import { ClientComponent } from './client/client.component';
import { FormClientComponent } from "./form-client/form-client.component";
import { ClientDeleteComponent } from "./client-delete/client-delete.component";

export const CLIENT_ROUTES: Routes = [
  {
    path: '',
    children: [
      { path: 'list', component: ClientComponent },
      { path: 'add', component: FormClientComponent },
      { path: 'edit/:id', component: FormClientComponent },
      { path: 'delete/:id', component: ClientDeleteComponent },
      { path: '**', redirectTo: 'list' }
    ]
  }
];
