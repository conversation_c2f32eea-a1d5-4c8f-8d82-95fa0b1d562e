import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';
import { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';

@Injectable({
  providedIn: 'root'
})
export class ClientService {
  private baseUrl: string = 'https://localhost:5001/api/Client/';
  private currentClientSubject: BehaviorSubject<Client | null>;
  public currentClient$: Observable<Client | null>;
  dataChange = new BehaviorSubject<Client[]>([]);
  dialogData!: Client;
  isTblLoading = true;

  constructor(private http: HttpClient, private router: Router) {
    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());
    this.currentClient$ = this.currentClientSubject.asObservable();
  }

  private getClientFromStorage(): Client | null {
    const clientData = localStorage.getItem('currentClient');
    return clientData ? JSON.parse(clientData) : null;
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token');
    return new HttpHeaders({
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {})
    });
  }

  // Récupérer tous les clients
  getAllClients(): Observable<Client[]> {
    this.isTblLoading = true;
    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(
      tap(clients => {
        this.isTblLoading = false;
        this.dataChange.next(clients || []);
        console.log('Clients récupérés du serveur:', clients?.length || 0);
      }),
      catchError(error => {
        this.isTblLoading = false;
        console.error('Erreur lors de la récupération des clients:', error);

        // En cas d'erreur de connexion, utiliser des données de test
        if (error.status === 0) {
          console.log('Serveur non disponible, utilisation de données de test');
          const testClients: Client[] = [
            {
              id: '1',
              code: 'CLI001',
              syntax: 'Client Test 1',
              matFiscal: '*********',
              email: '<EMAIL>',
              telephone: '12345678'
            },
            {
              id: '2',
              code: 'CLI002',
              syntax: 'Client Test 2',
              matFiscal: '*********',
              email: '<EMAIL>',
              telephone: '87654321'
            }
          ];
          this.dataChange.next(testClients);
          return new Observable<Client[]>(observer => {
            observer.next(testClients);
            observer.complete();
          });
        }

        // Pour les autres erreurs, mettre un tableau vide
        this.dataChange.next([]);
        return this.handleError(error);
      })
    );
  }

  // Récupérer un client par son ID
  getClientById(id: string): Observable<Client> {
    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(
      tap(client => {
        localStorage.setItem('currentClient', JSON.stringify(client));
        this.currentClientSubject.next(client);
      }),
      catchError(this.handleError)
    );
  }

  // Créer un nouveau client
  createClient(client: CreateClientSimpleDto): Observable<Client> {
    // Validation des données d'entrée
    if (!client) {
      return throwError(() => new Error('Les données du client sont requises'));
    }

    if (!client.code || client.code.trim() === '') {
      return throwError(() => new Error('Le code client est requis'));
    }

    if (client.email && !this.validateEmail(client.email)) {
      return throwError(() => new Error('Format d\'email invalide'));
    }

    // Préparer les données à envoyer
    const clientData: CreateClientSimpleDto = {
      id: client.id || this.generateGuid(),
      code: client.code.trim(),
      syntax: client.syntax?.trim(),
      matFiscal: client.matFiscal?.trim(),
      email: client.email?.trim(),
      telephone: client.telephone?.trim()
    };

    return this.http.post<Client>(this.baseUrl, clientData, {
      headers: this.getHeaders(),
      observe: 'response'
    }).pipe(
      map((response: any) => {
        const newClient: Client = response.body;
        if (!newClient) {
          throw new Error('Aucune donnée reçue du serveur');
        }
        return newClient;
      }),
      tap((newClient: Client) => {
        const currentData = this.dataChange.value;
        this.dataChange.next([...currentData, newClient]);
      }),
      catchError(this.handleCreateError)
    );
  }

  // Mettre à jour un client
  updateClient(id: string, client: UpdateClientDto): Observable<Client> {
    // Validation des données d'entrée
    if (!id || id.trim() === '') {
      return throwError(() => new Error('ID du client requis pour la mise à jour'));
    }

    if (!client) {
      return throwError(() => new Error('Les données du client sont requises'));
    }

    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire
    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
    const isCode = /^[A-Z]{3}-\d{3}$/.test(id);

    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);

    // Si l'ID est un code client, essayer de trouver le GUID correspondant
    let actualId = id;
    if (isCode && !isGuid) {
      const currentData = this.dataChange.value;
      const clientByCode = currentData.find(c => c.code === id);
      if (clientByCode && clientByCode.id !== id) {
        console.log('ID trouvé par code:', clientByCode.id);
        actualId = clientByCode.id;
      }
    }

    // Nettoyer et valider les données
    const cleanedClient: UpdateClientDto = {};

    // S'assurer que l'id n'est jamais inclus dans les données à envoyer
    const clientData = { ...client };
    if ('id' in clientData) {
      delete (clientData as any).id;
    }

    // Ajouter seulement les champs non vides et valides
    if (clientData.code && clientData.code.trim()) {
      cleanedClient.code = clientData.code.trim();
      // Validation du code
      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {
        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));
      }
    }

    if (clientData.syntax && clientData.syntax.trim()) {
      cleanedClient.syntax = clientData.syntax.trim();
      if (cleanedClient.syntax.length > 100) {
        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));
      }
    }

    if (clientData.matFiscal && clientData.matFiscal.trim()) {
      cleanedClient.matFiscal = clientData.matFiscal.trim().toUpperCase();
      // Validation du matricule fiscal
      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;
      if (!matFiscalPattern.test(cleanedClient.matFiscal)) {
        return throwError(() => new Error('Format de matricule fiscal invalide'));
      }
    }

    if (clientData.email && clientData.email.trim()) {
      cleanedClient.email = clientData.email.trim().toLowerCase();
      // Validation de l'email
      if (!this.validateEmail(cleanedClient.email)) {
        return throwError(() => new Error('Format d\'email invalide'));
      }
      if (cleanedClient.email.length > 100) {
        return throwError(() => new Error('L\'email ne peut pas dépasser 100 caractères'));
      }
    }

    if (clientData.telephone && clientData.telephone.trim()) {
      cleanedClient.telephone = clientData.telephone.trim();
      // Validation du téléphone
      const phonePattern = /^[+]?[0-9\s\-\(\)\.]{8,20}$/;
      if (!phonePattern.test(cleanedClient.telephone)) {
        return throwError(() => new Error('Format de téléphone invalide'));
      }
    }

    // Vérifier qu'au moins un champ est fourni pour la mise à jour
    if (Object.keys(cleanedClient).length === 0) {
      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));
    }

    // Validation stricte côté client pour éviter les erreurs serveur
    const validationResult = this.validateUpdateData(cleanedClient, actualId);
    if (!validationResult.isValid) {
      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));
    }

    console.log('=== MISE À JOUR CLIENT ===');
    console.log('ID original:', id);
    console.log('ID à utiliser:', actualId);
    console.log('Type de l\'ID:', typeof actualId);
    console.log('Longueur de l\'ID:', actualId.length);
    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));
    console.log('ID est un code?', /^[A-Z]{3}-\d{3}$/.test(actualId));
    console.log('Données originales:', client);
    console.log('Données après suppression ID:', clientData);
    console.log('Données nettoyées à envoyer:', cleanedClient);
    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');
    console.log('URL complète:', `${this.baseUrl}${actualId}`);
    console.log('Headers:', this.getHeaders());
    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));
    console.log('========================');

    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {
      headers: this.getHeaders(),
      observe: 'response'
    }).pipe(
      map((response: any) => {
        // Si le serveur retourne NoContent (204), créer le client mis à jour localement
        if (response.status === 204 || !response.body) {
          const currentData = this.dataChange.value;
          // Chercher par l'ID original ou l'ID actuel
          const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);
          if (!existingClient) {
            throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);
          }

          // Créer le client mis à jour en combinant les données existantes et les nouvelles
          const updatedClient: Client = {
            ...existingClient,
            ...cleanedClient
          };
          return updatedClient;
        }

        // Si le serveur retourne le client mis à jour
        const updatedClient: Client = response.body;
        return updatedClient;
      }),
      tap((updatedClient: Client) => {
        console.log('Client mis à jour avec succès:', updatedClient);

        // Mettre à jour les données locales
        const currentData = this.dataChange.value;
        const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);
        if (index !== -1) {
          currentData[index] = updatedClient;
          this.dataChange.next([...currentData]);
        }

        // Mettre à jour le client courant si c'est le même
        if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {
          this.currentClientSubject.next(updatedClient);
          localStorage.setItem('currentClient', JSON.stringify(updatedClient));
        }
      }),
      catchError(error => {
        console.error('Erreur lors de la mise à jour:', error);

        // Si le serveur n'est pas disponible, simuler la mise à jour localement
        if (error.status === 0) {
          console.log('Serveur non disponible, simulation de la mise à jour...');
          return this.simulateLocalUpdate(actualId, cleanedClient);
        }

        // TEMPORAIRE: Désactiver la simulation pour voir l'erreur réelle
        // const errorMessage = error.error?.message || error.error?.title || '';
        // if (error.status === 400 && errorMessage.includes('validation errors')) {
        //   console.warn('Erreur de validation serveur persistante, simulation locale...');
        //   return this.simulateLocalUpdate(actualId, cleanedClient);
        // }

        console.error('ERREUR 400 - PAS DE SIMULATION, ERREUR RÉELLE:');
        console.error('Détails complets de l\'erreur:', error);

        return this.handleUpdateError(error);
      })
    );
  }

  // Supprimer un client
  deleteClient(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(
      tap(() => {
        this.clearCurrentClient();
      }),
      catchError(this.handleError)
    );
  }

  // Supprimer plusieurs clients
  deleteSelectedClients(ids: string[]): Observable<any> {
    if (!ids || ids.length === 0) {
      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));
    }

    console.log('Tentative de suppression des clients:', ids);

    return this.http.delete(`${this.baseUrl}delete-selected`, {
      headers: this.getHeaders(),
      body: ids
    }).pipe(
      tap(() => {
        console.log('Suppression en masse réussie');
        // Mettre à jour les données locales
        const currentData = this.dataChange.value;
        const updatedData = currentData.filter(client => !ids.includes(client.id));
        this.dataChange.next(updatedData);
      }),
      catchError(error => {
        console.error('Erreur lors de la suppression en masse:', error);

        // Si le serveur n'est pas disponible, simuler la suppression localement
        if (error.status === 0) {
          console.log('Serveur non disponible, simulation de la suppression...');
          return this.simulateLocalDeletion(ids);
        }

        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle
        if (error.status === 500 || error.status === 404) {
          console.log('Tentative de suppression individuelle...');
          return this.deleteClientsIndividually(ids);
        }

        return this.handleError(error);
      })
    );
  }

  // Méthode de fallback pour supprimer les clients individuellement
  private deleteClientsIndividually(ids: string[]): Observable<any> {
    const deleteRequests = ids.map(id =>
      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(
        catchError(error => {
          console.error(`Erreur lors de la suppression du client ${id}:`, error);
          return throwError(() => error);
        })
      )
    );

    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle
    return new Observable(observer => {
      let completedCount = 0;
      let hasError = false;
      const errors: any[] = [];

      deleteRequests.forEach((request, index) => {
        request.subscribe({
          next: () => {
            completedCount++;
            if (completedCount === ids.length && !hasError) {
              // Mettre à jour les données locales
              const currentData = this.dataChange.value;
              const updatedData = currentData.filter(client => !ids.includes(client.id));
              this.dataChange.next(updatedData);
              observer.next({ deletedCount: completedCount });
              observer.complete();
            }
          },
          error: (error) => {
            hasError = true;
            errors.push({ id: ids[index], error });
            if (completedCount + errors.length === ids.length) {
              observer.error({
                message: 'Certains clients n\'ont pas pu être supprimés',
                errors,
                deletedCount: completedCount
              });
            }
          }
        });
      });
    });
  }

  // Simuler la suppression locale quand le serveur n'est pas disponible
  private simulateLocalDeletion(ids: string[]): Observable<any> {
    console.log('Simulation de la suppression locale pour les IDs:', ids);

    // Mettre à jour les données locales
    const currentData = this.dataChange.value;
    const updatedData = currentData.filter(client => !ids.includes(client.id));
    this.dataChange.next(updatedData);

    // Retourner un Observable qui simule le succès
    return new Observable(observer => {
      setTimeout(() => {
        observer.next({
          deletedCount: ids.length,
          message: 'Suppression simulée localement (serveur non disponible)'
        });
        observer.complete();
      }, 500); // Simuler un délai réseau
    });
  }

  // Simuler la mise à jour locale quand le serveur n'est pas disponible
  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {
    console.log('Simulation de la mise à jour locale pour l\'ID:', id, updateData);

    // Trouver et mettre à jour le client dans les données locales
    const currentData = this.dataChange.value;
    const index = currentData.findIndex(client => client.id === id);

    if (index === -1) {
      return throwError(() => new Error('Client non trouvé pour la mise à jour'));
    }

    // Créer le client mis à jour
    const updatedClient: Client = {
      ...currentData[index],
      ...updateData
    };

    // Mettre à jour les données locales
    currentData[index] = updatedClient;
    this.dataChange.next([...currentData]);

    // Mettre à jour le client courant si c'est le même
    if (this.currentClientSubject.value?.id === id) {
      this.currentClientSubject.next(updatedClient);
      localStorage.setItem('currentClient', JSON.stringify(updatedClient));
    }

    // Retourner un Observable qui simule le succès
    return new Observable<Client>(observer => {
      setTimeout(() => {
        observer.next(updatedClient);
        observer.complete();
      }, 500); // Simuler un délai réseau
    });
  }

  // Effacer le client courant
  clearCurrentClient(): void {
    localStorage.removeItem('currentClient');
    this.currentClientSubject.next(null);
  }

  get data(): Client[] {
    return this.dataChange.value;
  }

  getDialogData() {
    return this.dialogData;
  }

  // Méthodes utilitaires
  private generateGuid(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0,
        v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private validateEmail(email: string): boolean {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }

  // Méthode de test pour diagnostiquer les problèmes d'ID
  public testClientId(id: string): void {
    console.log('=== TEST ID CLIENT ===');
    console.log('ID fourni:', id);
    console.log('Type:', typeof id);
    console.log('Longueur:', id.length);
    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));
    console.log('Est code:', /^[A-Z]{3}-\d{3}$/.test(id));

    const currentData = this.dataChange.value;
    const clientById = currentData.find(c => c.id === id);
    const clientByCode = currentData.find(c => c.code === id);

    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');
    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');

    if (clientByCode) {
      console.log('Client par code:', clientByCode);
    }
    console.log('===================');
  }

  // Méthode de test pour valider une mise à jour sans l'envoyer
  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {
    console.log('=== TEST VALIDATION MISE À JOUR ===');
    console.log('ID:', id);
    console.log('Données:', updateData);

    const validation = this.validateUpdateData(updateData, id);
    console.log('Validation réussie:', validation.isValid);
    if (!validation.isValid) {
      console.log('Erreurs:', validation.errors);
    }

    console.log('Données nettoyées qui seraient envoyées:');
    const cleaned: UpdateClientDto = {};
    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();
    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();
    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();
    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();
    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();

    console.log('Cleaned data:', cleaned);
    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);
    console.log('================================');
  }

  // Validation stricte pour la mise à jour
  private validateUpdateData(client: UpdateClientDto, id: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validation de l'ID
    if (!id || id.trim() === '') {
      errors.push('ID requis');
    } else {
      const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
      if (!isGuid) {
        errors.push('ID doit être un GUID valide');
      }
    }

    // Validation des champs
    if (client.code !== undefined) {
      if (!client.code || client.code.trim() === '') {
        errors.push('Code ne peut pas être vide');
      } else if (client.code.length < 2 || client.code.length > 20) {
        errors.push('Code doit contenir entre 2 et 20 caractères');
      }
    }

    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {
      errors.push('Raison sociale ne peut pas dépasser 100 caractères');
    }

    if (client.matFiscal !== undefined && client.matFiscal !== null) {
      if (client.matFiscal.trim() !== '') {
        const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;
        if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {
          errors.push('Format de matricule fiscal invalide');
        }
      }
    }

    if (client.email !== undefined && client.email !== null) {
      if (client.email.trim() !== '') {
        if (!this.validateEmail(client.email)) {
          errors.push('Format d\'email invalide');
        }
        if (client.email.length > 100) {
          errors.push('Email ne peut pas dépasser 100 caractères');
        }
      }
    }

    if (client.telephone !== undefined && client.telephone !== null) {
      if (client.telephone.trim() !== '') {
        const phonePattern = /^[+]?[0-9\s\-\(\)\.]{8,20}$/;
        if (!phonePattern.test(client.telephone)) {
          errors.push('Format de téléphone invalide');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Méthode publique pour tester une mise à jour complète
  public debugUpdate(clientId: string, updateData: any): void {
    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');

    // Test 1: Validation de l'ID
    this.testClientId(clientId);

    // Test 2: Validation des données
    this.testUpdateValidation(clientId, updateData);

    // Test 3: Simulation de la requête
    console.log('--- Simulation de la requête ---');
    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);
    const isCode = /^[A-Z]{3}-\d{3}$/.test(clientId);

    let actualId = clientId;
    if (isCode && !isGuid) {
      const currentData = this.dataChange.value;
      const clientByCode = currentData.find(c => c.code === clientId);
      if (clientByCode) {
        actualId = clientByCode.id;
        console.log('ID résolu:', actualId);
      }
    }

    console.log('URL finale:', `${this.baseUrl}${actualId}`);
    console.log('Méthode: PUT');
    console.log('Headers:', this.getHeaders());

    const cleanedData: any = {};
    if (updateData.code) cleanedData.code = updateData.code.trim();
    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();
    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim().toUpperCase();
    if (updateData.email) cleanedData.email = updateData.email.trim().toLowerCase();
    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();

    console.log('Body final:', JSON.stringify(cleanedData, null, 2));
    console.log('================================');
  }

  // Méthode pour tester la requête HTTP brute
  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {
    console.log('=== TEST REQUÊTE HTTP BRUTE ===');

    const url = `${this.baseUrl}${clientId}`;
    const headers = this.getHeaders();

    console.log('URL:', url);
    console.log('Headers:', headers);
    console.log('Body:', JSON.stringify(updateData, null, 2));

    return this.http.put(url, updateData, {
      headers: headers,
      observe: 'response'
    }).pipe(
      tap(response => {
        console.log('SUCCÈS - Response:', response);
      }),
      catchError(error => {
        console.error('ÉCHEC - Error détaillé:', error);
        console.error('Status:', error.status);
        console.error('Error body:', error.error);

        // Retourner l'erreur pour que l'appelant puisse la voir
        return throwError(() => error);
      })
    );
  }

  // Test avec des données minimales pour identifier le problème
  public testMinimalUpdate(clientId: string): Observable<any> {
    console.log('=== TEST MISE À JOUR MINIMALE ===');

    // Test 1: Seulement le code
    const minimalData1 = { code: 'TEST001' };
    console.log('Test 1 - Seulement code:', minimalData1);

    return this.testRawHttpRequest(clientId, minimalData1).pipe(
      catchError(error1 => {
        console.log('Test 1 échoué, essai test 2...');

        // Test 2: Seulement la syntax
        const minimalData2 = { syntax: 'Test Client' };
        console.log('Test 2 - Seulement syntax:', minimalData2);

        return this.testRawHttpRequest(clientId, minimalData2).pipe(
          catchError(error2 => {
            console.log('Test 2 échoué, essai test 3...');

            // Test 3: Objet vide
            const minimalData3 = {};
            console.log('Test 3 - Objet vide:', minimalData3);

            return this.testRawHttpRequest(clientId, minimalData3);
          })
        );
      })
    );
  }

  // Méthode utilitaire pour tester la validation des données client
  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (client.code) {
      if (client.code.length < 2 || client.code.length > 20) {
        errors.push('Le code client doit contenir entre 2 et 20 caractères');
      }
    }

    if (client.syntax && client.syntax.length > 100) {
      errors.push('La raison sociale ne peut pas dépasser 100 caractères');
    }

    if (client.matFiscal) {
      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;
      if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {
        errors.push('Format de matricule fiscal invalide');
      }
    }

    if (client.email) {
      if (!this.validateEmail(client.email)) {
        errors.push('Format d\'email invalide');
      }
      if (client.email.length > 100) {
        errors.push('L\'email ne peut pas dépasser 100 caractères');
      }
    }

    if (client.telephone) {
      const phonePattern = /^[+]?[0-9\s\-\(\)\.]{8,20}$/;
      if (!phonePattern.test(client.telephone)) {
        errors.push('Format de téléphone invalide');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Gestion des erreurs
  private handleUpdateError(error: HttpErrorResponse) {
    let errorMessage = 'Erreur lors de la mise à jour du client';

    console.error('=== ERREUR DE MISE À JOUR ===');
    console.error('Status:', error.status);
    console.error('StatusText:', error.statusText);
    console.error('URL:', error.url);
    console.error('Error object:', error.error);
    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');
    console.error('Full error:', error);
    console.error('============================');

    // Analyser la structure de l'erreur en détail
    const apiError = error.error?.message || error.error?.title || error.error?.error;
    const validationErrors = error.error?.errors;
    const traceId = error.error?.traceId;
    const type = error.error?.type;

    console.log('API Error:', apiError);
    console.log('Validation Errors:', validationErrors);
    console.log('Validation Errors type:', typeof validationErrors);
    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');
    console.log('Trace ID:', traceId);
    console.log('Error Type:', type);

    // Essayer de capturer d'autres propriétés d'erreur
    if (error.error) {
      console.log('Toutes les propriétés de error.error:');
      for (const key in error.error) {
        console.log(`  ${key}:`, error.error[key]);
      }
    }

    if (error.status === 0) {
      errorMessage = 'Impossible de se connecter au serveur';
    } else if (error.status === 400) {
      errorMessage = 'Données invalides pour la mise à jour';

      // Gestion spécifique des erreurs de validation ASP.NET Core
      if (validationErrors) {
        const errorDetails: string[] = [];

        // Si c'est un objet avec des propriétés (format ASP.NET Core)
        if (typeof validationErrors === 'object') {
          Object.keys(validationErrors).forEach(field => {
            const fieldErrors = validationErrors[field];
            if (Array.isArray(fieldErrors)) {
              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));
            } else {
              errorDetails.push(`${field}: ${fieldErrors}`);
            }
          });
        }

        if (errorDetails.length > 0) {
          errorMessage += `\n\nDétails:\n${errorDetails.join('\n')}`;
        }
      } else if (apiError) {
        errorMessage += `\n\nDétail: ${apiError}`;
      }

      // Cas spécifique pour "One or more validation errors occurred"
      if (apiError && apiError.includes('validation errors occurred')) {
        errorMessage = 'Erreurs de validation:\n';
        if (error.error?.errors) {
          errorMessage += 'Veuillez vérifier les champs suivants:\n';
          Object.keys(error.error.errors).forEach(field => {
            errorMessage += `- ${field}\n`;
          });
        } else {
          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';
        }
      }
    } else if (error.status === 401) {
      errorMessage = 'Session expirée';
      this.router.navigate(['/login']);
    } else if (error.status === 404) {
      errorMessage = 'Client non trouvé';
    } else if (error.status === 409) {
      errorMessage = 'Conflit - un autre client avec ces données existe déjà';
    } else if (apiError) {
      errorMessage = `Erreur de mise à jour: ${apiError}`;
    }

    return throwError(() => new Error(errorMessage));
  }

  private handleCreateError(error: HttpErrorResponse) {
    let errorMessage = 'Erreur lors de la création du client';
    const apiError = error.error?.message || error.error?.title || error.error?.error;

    if (apiError) {
      errorMessage = `Erreur de création: ${apiError}`;
    } else if (error.status === 0) {
      errorMessage = 'Impossible de se connecter au serveur';
    } else if (error.status === 400) {
      errorMessage = 'Données invalides';
      if (error.error?.errors) {
        const validationErrors = Object.values(error.error.errors).flat();
        errorMessage += ` Détails: ${validationErrors.join(', ')}`;
      }
    } else if (error.status === 401) {
      errorMessage = 'Session expirée';
      this.router.navigate(['/login']);
    } else if (error.status === 409) {
      errorMessage = 'Un client avec cet ID existe déjà';
    }

    return throwError(() => new Error(errorMessage));
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An error occurred';
    const apiError = error.error?.message || error.error?.title;

    if (apiError) {
      errorMessage = apiError;
    } else if (error.status === 0) {
      errorMessage = 'Unable to connect to server';
    } else if (error.status === 400) {
      errorMessage = 'Invalid request data';
    } else if (error.status === 401) {
      errorMessage = 'Unauthorized';
      this.router.navigate(['/login']);
    } else if (error.status === 404) {
      errorMessage = 'Client not found';
    } else if (error.status === 409) {
      errorMessage = 'Conflict - client already exists';
    }

    return throwError(() => new Error(errorMessage));
  }
}