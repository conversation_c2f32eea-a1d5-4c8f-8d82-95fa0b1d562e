import { Route } from '@angular/router';
import { MainLayoutComponent } from './layout/app-layout/main-layout/main-layout.component';
import { AuthGuard } from '@core/guard/auth.guard';
import { AuthLayoutComponent } from './layout/app-layout/auth-layout/auth-layout.component';
import { Page404Component } from './authentication/page404/page404.component';

export const APP_ROUTE: Route[] = [
  {
    path: '',
    component: MainLayoutComponent,
    canActivate: [AuthGuard], // Protection globale pour toutes les routes enfants
    children: [
      { path: '', redirectTo: '/admin', pathMatch: 'full' }, // Redirection vers admin par défaut

      // Route admin
      {
path: 'admin/gestion',
    canActivate: [AuthGuard],
    loadChildren: () => import('./admin/gestion_Produit/produit.routes').then(m => m.PRODUIT_ROUTES)
      },
            {
path: 'admin/gestion_Client',
    canActivate: [AuthGuard],
    loadChildren: () => import('./admin/gestion_Client/client.routes').then(m => m.CLIENT_ROUTES)
      },
      // Route admin
      {
        path: 'admin',
        loadChildren: () => import('./admin/admin.routes').then((m) => m.ADMIN_ROUTE),
      },

      // Route employee
      {
        path: 'employee',
        loadChildren: () => import('./employee/employee.routes').then((m) => m.EMPLOYEE_ROUTE),
      },

      // Route client
      {
        path: 'client',
        loadChildren: () => import('./client/client.routes').then((m) => m.CLIENT_ROUTE),
      },

      // Autres modules fonctionnels
      {
        path: 'calendar',
        loadChildren: () => import('./calendar/calendar.routes').then((m) => m.CALENDAR_ROUTE),
      },
      {
        path: 'task',
        loadChildren: () => import('./task/task.routes').then((m) => m.TASK_ROUTE),
      },
      {
        path: 'contacts',
        loadChildren: () => import('./contacts/contacts.routes').then((m) => m.CONTACT_ROUTE),
      },
      {
        path: 'email',
        loadChildren: () => import('./email/email.routes').then((m) => m.EMAIL_ROUTE),
      },
      {
        path: 'apps',
        loadChildren: () => import('./apps/apps.routes').then((m) => m.APPS_ROUTE),
      },
      {
        path: 'widget',
        loadChildren: () => import('./widget/widget.routes').then((m) => m.WIDGET_ROUTE),
      },
      {
        path: 'ui',
        loadChildren: () => import('./ui/ui.routes').then((m) => m.UI_ROUTE),
      },
      {
        path: 'forms',
        loadChildren: () => import('./forms/forms.routes').then((m) => m.FORMS_ROUTE),
      },
      {
        path: 'tables',
        loadChildren: () => import('./tables/tables.routes').then((m) => m.TEBLES_ROUTE),
      },
      {
        path: 'charts',
        loadChildren: () => import('./charts/charts.routes').then((m) => m.CHART_ROUTE),
      },
      {
        path: 'timeline',
        loadChildren: () => import('./timeline/timeline.routes').then((m) => m.TIMELINE_ROUTE),
      },
      {
        path: 'icons',
        loadChildren: () => import('./icons/icons.routes').then((m) => m.ICONS_ROUTE),
      },
      {
        path: 'extra-pages',
        loadChildren: () => import('./extra-pages/extra-pages.routes').then((m) => m.EXTRA_PAGES_ROUTE),
      },
      {
        path: 'maps',
        loadChildren: () => import('./maps/maps.routes').then((m) => m.MAPS_ROUTE),
      },
      {
        path: 'multilevel',
        loadChildren: () => import('./multilevel/multilevel.routes').then((m) => m.MULTILEVEL_ROUTE),
      },
    ],
  },
  {
    path: 'authentication',
    component: AuthLayoutComponent,
    loadChildren: () => import('./authentication/auth.routes').then((m) => m.AUTH_ROUTE),
  },
  { path: '**', component: Page404Component },
];